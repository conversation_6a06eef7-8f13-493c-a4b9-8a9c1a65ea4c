{"cells": [{"cell_type": "code", "execution_count": null, "id": "7d87d631", "metadata": {}, "outputs": [], "source": ["# 최종 테스트 완료\n", "# # -*- coding: utf-8 -*-\n", "\"\"\"\n", "전자소송 크롤링 - 관심사건 일괄 수집(일반내용 전체문자열 + 진행내용)\n", "- 로그인: 탭 클릭 → ID 입력 → 가상 키패드로 비밀번호 입력 → 로그인\n", "- 일반내용: 컨테이너 탐색(JS) → 전체문자열 저장, 사건번호는 목록 a태그에서 우선 확보\n", "- 진행내용: 탭 클릭 → 테이블에서 '일자','내용','결과' 추출\n", "- 결과 저장: 구글 스프레드시트 2시트 업로드\n", "    · 사건 일반내용 크롤링 로그  : [사건번호, 전체문자열, 크롤링일시]   ← 마지막 열 자동 주입\n", "    · 사건 진행내역 크롤링 로그  : [사건번호, 일자, 내용, 결과, 크롤링일시] ← 마지막 열 자동 주입\n", "\"\"\"\n", "\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "import time, re, pyperclip, pandas as pd\n", "from datetime import datetime\n"]}, {"cell_type": "code", "execution_count": null, "id": "c5397d5f", "metadata": {}, "outputs": [], "source": ["# ===== 구글 스프레드시트 업로드 유틸 =====\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "\n", "SPREADSHEET_ID = \"1y4V6DRBobKltlc5NpRsLHN90bucEsIHqZdFm1VqeATY\"\n", "WS_PROG = \"사건 진행내역 크롤링 로그\"\n", "WS_GEN  = \"사건 일반내용 크롤링 로그\"\n", "\n", "# 업로드 템플릿 (마지막 열 '크롤링일시'는 업로드 시 자동 입력)\n", "TEMPLATE_GEN  = [\"사건번호\", \"전체문자열\", \"크롤링일시\"]\n", "TEMPLATE_PROG = [\"사건번호\", \"일자\", \"내용\", \"결과\", \"크롤링일시\"]\n", "TS_COL_NAME   = \"크롤링일시\"\n", "\n", "def _now_ts() -> str:\n", "    return datetime.now().strftime('%Y-%m-%d %H:%M')\n", "\n", "def get_client():\n", "    creds  = ServiceAccountCredentials.from_json_keyfile_name(\"credentials.json\", [\n", "        \"https://www.googleapis.com/auth/spreadsheets\",\n", "        \"https://www.googleapis.com/auth/drive\",\n", "    ])\n", "    return gspread.authorize(creds)\n", "\n", "def _open_ss_by_id(client, key: str):\n", "    try:\n", "        return client.open_by_key(key)\n", "    except gspread.SpreadsheetNotFound as e:\n", "        raise RuntimeError(\"스프레드시트를 ID로 열 수 없습니다. \"\n", "                           \"ID가 맞는지, 서비스계정이 편집자로 공유되었는지 확인하세요.\") from e\n", "\n", "def _open_or_create_ws(ss, title: str, rows=\"1000\", cols=\"26\"):\n", "    try:\n", "        return ss.worksheet(title)\n", "    except gspread.exceptions.WorksheetNotFound:\n", "        return ss.add_worksheet(title=title, rows=rows, cols=cols)\n", "\n", "def _ensure_header(ws, template_cols: list[str]):\n", "    if ws.get_all_values():\n", "        return\n", "    ws.append_row(template_cols)\n", "\n", "def _df_to_rows_with_ts(df: pd.DataFrame, template_cols: list[str]) -> list[list[str]]:\n", "    \"\"\"\n", "    템플릿 마지막 열이 TS_COL_NAME(=크롤링일시)이면 업로드 시 자동 주입.\n", "    → DF에는 '크롤링일시' 컬럼이 없어야 정상 동작(마지막 열은 우리가 채움).\n", "    \"\"\"\n", "    rows: list[list[str]] = []\n", "    is_ts_last = (template_cols and template_cols[-1] == TS_COL_NAME)\n", "    for _, r in df.iterrows():\n", "        ordered = []\n", "        for idx, col in enumerate(template_cols):\n", "            if is_ts_last and idx == len(template_cols) - 1:\n", "                ordered.append(_now_ts())  # 자동 타임스탬프\n", "            else:\n", "                ordered.append(\"\" if col not in df.columns or pd.isna(r.get(col, \"\")) else str(r[col]))\n", "        rows.append(ordered)\n", "    return rows\n", "\n", "def _append_rows(ws, rows: list[list[str]], batch_size: int = 500):\n", "    if not rows:\n", "        return\n", "    for start in range(0, len(rows), batch_size):\n", "        batch = rows[start:start+batch_size]\n", "        attempt, delay = 0, 1.0\n", "        while True:\n", "            try:\n", "                ws.append_rows(\n", "                    batch,\n", "                    value_input_option=\"RAW\",\n", "                    insert_data_option=\"INSERT_ROWS\",\n", "                    table_range=\"A1\"\n", "                )\n", "                break\n", "            except gspread.exceptions.APIError:\n", "                attempt += 1\n", "                if attempt > 5:\n", "                    raise\n", "                time.sleep(delay)\n", "                delay = min(delay * 2, 16)\n", "\n", "def upload_logs_to_sheets(df_prog: pd.DataFrame, df_gen: pd.DataFrame):\n", "    \"\"\"\n", "    df_gen : [사건번호, 전체문자열]\n", "    df_prog: [사건번호, 일자, 내용, 결과]\n", "    (둘 다 '크롤링일시'는 템플릿 마지막 열 자동 입력)\n", "    \"\"\"\n", "    client = get_client()\n", "    ss = _open_ss_by_id(client, SPREADSHEET_ID)\n", "\n", "    # 일반내용\n", "    ws_gen = _open_or_create_ws(ss, WS_GEN)\n", "    _ensure_header(ws_gen, TEMPLATE_GEN)\n", "    rows_gen = _df_to_rows_with_ts(df_gen, TEMPLATE_GEN)\n", "    _append_rows(ws_gen, rows_gen)\n", "\n", "    # 진행내용\n", "    ws_prog = _open_or_create_ws(ss, WS_PROG)\n", "    _ensure_header(ws_prog, TEMPLATE_PROG)\n", "    rows_prog = _df_to_rows_with_ts(df_prog, TEMPLATE_PROG)\n", "    _append_rows(ws_prog, rows_prog)\n", "\n", "    print(\"[✅] 구글 스프레드시트 업로드 완료 →\", WS_GEN, \"/\", WS_PROG)\n"]}, {"cell_type": "code", "execution_count": null, "id": "5bdca190", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 환경 상수\n", "# ─────────────────────────────────────────────────────\n", "url_login   = \"https://ecfs.scourt.go.kr/psp/index.on?m=PSP101M01\"\n", "tab_btn_id  = \"mf_pfwork_tabctrl_tab_tabs2_tabHTML\"      # '아이디 로그인' 탭\n", "id_box_id   = \"mf_pfwork_ibx_elpUserId\"\n", "pwd_box_id  = \"mf_pfwork_ibx_elpUserPwd\"\n", "login_btnid = \"mf_pfwork_btn_login\"\n", "keypad_div_id = f\"nppfs-keypad-{pwd_box_id}\"\n", "\n", "user_id     = \"lawyer87\"\n", "password    = \"kkcho0904!\"\n", "\n", "CASE_ROWS_XPATH = \"//table[contains(@class,'grid')]/tbody/tr\"\n", "CASE_ANCHOR_REL = \".//a[contains(@class,'link') or self::a]\"\n", "\n", "PROG_TAB_ID   = \"mf_wfSsgoDetail_ssgoCsDetailTab_tab_ssgoTab2_tabHTML\"\n", "PROG_TABLE_ID = \"mf_wfSsgoDetail_ssgoCsDetailTab_contents_ssgoTab2_body_grd_csProgLst_body_grd_csProgLst_body_table\"\n", "\n", "# 사건번호 패턴\n", "CASE_NO_RE = re.compile(r'(?:19|20)\\d{2}\\s*[가-힣]{1,4}\\s*\\d{1,7}')\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 셀레니움 기본 설정\n", "# ─────────────────────────────────────────────────────\n", "chrome_opts = Options()\n", "chrome_opts.add_argument(\"--start-maximized\")\n", "chrome_opts.add_experimental_option(\"detach\", True)\n", "driver = webdriver.Chrome(options=chrome_opts)\n", "wait   = WebDriverWait(driver, 10)\n", "action = <PERSON><PERSON><PERSON><PERSON>(driver)\n"]}, {"cell_type": "code", "execution_count": null, "id": "86baca56", "metadata": {}, "outputs": [], "source": ["\n", "# ─────────────────────────────────────────────────────\n", "# 가상 키패드 입력\n", "# ─────────────────────────────────────────────────────\n", "def _enter_pwd(pwd):\n", "    try:\n", "        iframe = driver.find_element(By.XPATH, f'//iframe[contains(@id,\"{keypad_div_id}\")]')\n", "        driver.switch_to.frame(iframe)\n", "    except:\n", "        pass\n", "    wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR,'div.kpd-group.lower')))\n", "    for ch in pwd:\n", "        label = ch if ch.isdigit() else (f\"소문자 {ch}\" if ch.isalpha() else \"느낌표\")\n", "        if ch == '!':\n", "            try:\n", "                wait.until(EC.element_to_be_clickable((By.XPATH,'//img[@aria-label=\"느낌표\"]')))\n", "            except:\n", "                toggle = driver.find_element(By.XPATH,'//div[contains(@class,\"kpd-group lower\")]//img[@aria-label=\"특수문자\"]')\n", "                driver.execute_script(\"arguments[0].click();\", toggle)\n", "                wait.until(EC.element_to_be_clickable((By.XPATH,'//img[@aria-label=\"느낌표\"]')))\n", "        btn = wait.until(EC.element_to_be_clickable((By.XPATH,f'//img[@aria-label=\"{label}\"]')))\n", "        driver.execute_script(\"arguments[0].click();\", btn)\n", "        time.sleep(0.03)\n", "    for xp in (f'//*[@id=\"{keypad_div_id}\"]/div/div[5]/img[39]', '//img[@data-action=\"action:enter\"]'):\n", "        try:\n", "            btn = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.XPATH, xp)))\n", "            driver.execute_script(\"arguments[0].click();\", btn)\n", "            break\n", "        except:\n", "            continue\n", "    driver.switch_to.default_content()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e58afedd", "metadata": {}, "outputs": [], "source": ["\n", "# ─────────────────────────────────────────────────────\n", "# 유틸/네비게이션\n", "# ─────────────────────────────────────────────────────\n", "def login():\n", "    driver.get(url_login)\n", "    wait.until(EC.element_to_be_clickable((By.ID, tab_btn_id))).click()\n", "    time.sleep(0.8)\n", "    wait.until(EC.element_to_be_clickable((By.ID, id_box_id))).send_keys(user_id)\n", "    driver.find_element(By.ID, pwd_box_id).click()\n", "    wait.until(EC.visibility_of_element_located((By.ID, keypad_div_id)))\n", "    _enter_pwd(password)\n", "    wait.until(EC.element_to_be_clickable((By.ID, login_btnid))).click()\n", "    print(\"[✅] 로그인 완료\")\n", "\n", "def go_to_my_ecfs():\n", "    btn = wait.until(EC.element_to_be_clickable((By.ID, \"mf_pmf_content1_wq_uuid_286\")))\n", "    try: btn.click()\n", "    except: driver.execute_script(\"arguments[0].click();\", btn)\n", "    wait.until(EC.any_of(\n", "        EC.presence_of_element_located((By.ID, \"mf_pfmenu_v3_li_a_150202\")),\n", "        EC.presence_of_element_located((By.XPATH, \"//h2[contains(.,'나의 전자소송')]\"))\n", "    ))\n", "    print(\"[✅] 나의 전자소송 클릭 완료\")\n", "\n", "def go_to_interest_case():\n", "    time.sleep(0.5)\n", "    driver.find_element(By.ID, \"mf_pfmenu_v3_li_a_150202\").click()\n", "    driver.find_element(By.ID, \"mf_pfwork_sbx_cortList\").click()\n", "    driver.find_element(By.XPATH, '//*[@id=\"mf_pfwork_sbx_cortList\"]/option[2]').click()\n", "    time.sleep(1.5)\n", "    driver.find_element(By.ID, \"mf_pfwork_btn_search\").click()\n", "    print(\"[✅] 관심사건 조회 완료\")\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 사건번호 파싱/정제\n", "# ─────────────────────────────────────────────────────\n", "def normalize_case_no(s: str) -> str:\n", "    return re.sub(r\"\\s+\", \"\", s or \"\").strip()\n", "\n", "def pick_case_no_from_text(text: str) -> str:\n", "    if not text:\n", "        return \"\"\n", "    m = CASE_NO_RE.search(text)\n", "    if not m:\n", "        return \"\"\n", "    return normalize_case_no(m.group(0))\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 목록 → 상세 (사건번호 함께 반환)\n", "# ─────────────────────────────────────────────────────\n", "def click_case_by_index(i: int) -> str:\n", "    rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, CASE_ROWS_XPATH)))\n", "    if i < 0 or i >= len(rows):\n", "        raise IndexError(\"사건 인덱스 범위를 벗어났습니다.\")\n", "    anchor = rows[i].find_element(By.XPATH, CASE_ANCHOR_REL)\n", "    case_no_from_list = normalize_case_no(anchor.text)\n", "    driver.execute_script(\"arguments[0].click();\", anchor)\n", "    time.sleep(0.6)\n", "    if len(driver.window_handles) > 1:\n", "        driver.switch_to.window(driver.window_handles[-1])  # 헬퍼 없이 최신 창으로 전환\n", "\n", "    def has_anchor_here():\n", "        try:\n", "            driver.find_element(By.XPATH, \"//*[contains(normalize-space(.),'사건번호')]\")\n", "            return True\n", "        except:\n", "            return False\n", "\n", "    if not has_anchor_here():\n", "        driver.switch_to.default_content()\n", "        frames = driver.find_elements(By.TAG_NAME, \"iframe\")\n", "        for fr in frames:\n", "            try:\n", "                driver.switch_to.default_content()\n", "                driver.switch_to.frame(fr)\n", "                if has_anchor_here():\n", "                    return case_no_from_list\n", "            except:\n", "                continue\n", "        driver.switch_to.default_content()\n", "\n", "    return case_no_from_list\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 일반내용 추출(컨테이너 → 화면 전체 → 키입력 백업)\n", "# ─────────────────────────────────────────────────────\n", "def extract_general_text() -> str:\n", "    def _try_here():\n", "        try:\n", "            anchor = wait.until(EC.presence_of_element_located(\n", "                (By.XPATH, \"//*[contains(normalize-space(.),'사건번호')]\")\n", "            ))\n", "        except Exception:\n", "            return \"\"\n", "        try:\n", "            txt = driver.execute_script(\"\"\"\n", "                const anchor = arguments[0];\n", "                const ALLOWED = new Set(['DIV','SECTION','ARTICLE','MAIN','TABLE','TBODY','TR','TD','FORM']);\n", "                let el = anchor;\n", "                while (el && !ALLOWED.has(el.tagName)) el = el.parentElement;\n", "                const cont = el || anchor;\n", "                return (cont.innerText || '').trim();\n", "            \"\"\", anchor) or \"\"\n", "            if len(txt) >= 10:\n", "                return txt\n", "        except Exception:\n", "            pass\n", "        try:\n", "            body = (driver.execute_script(\"return (document.body.innerText || '').trim();\") or \"\")\n", "            if len(body) >= 10:\n", "                return body\n", "        except Exception:\n", "            pass\n", "        return \"\"\n", "\n", "    t = _try_here()\n", "    if len(t) >= 10:\n", "        return t\n", "\n", "    try:\n", "        driver.switch_to.default_content()\n", "        frames = driver.find_elements(By.TAG_NAME, \"iframe\")\n", "        for fr in frames:\n", "            try:\n", "                driver.switch_to.default_content()\n", "                driver.switch_to.frame(fr)\n", "                t = _try_here()\n", "                if len(t) >= 10:\n", "                    return t\n", "            except Exception:\n", "                continue\n", "    finally:\n", "        try: driver.switch_to.default_content()\n", "        except: pass\n", "\n", "    try:\n", "        body_el = wait.until(EC.presence_of_element_located((By.TAG_NAME, \"body\")))\n", "        body_el.click()\n", "        time.sleep(0.2)\n", "        action.key_down(Keys.CONTROL).send_keys(\"a\").key_up(Keys.CONTROL).perform()\n", "        time.sleep(0.2)\n", "        action.key_down(Keys.CONTROL).send_keys(\"c\").key_up(Keys.CONTROL).perform()\n", "        time.sleep(0.2)\n", "        from_text = (pyperclip.paste() or \"\").strip()\n", "        if len(from_text) >= 10:\n", "            return from_text\n", "    except Exception:\n", "        pass\n", "\n", "    return \"\"\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 진행내용 파싱\n", "# ─────────────────────────────────────────────────────\n", "def click_progress_tab_and_extract_table(case_no: str) -> list[dict]:\n", "    out_rows = []\n", "    try:\n", "        tab = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, PROG_TAB_ID)))\n", "        try:\n", "            tab.click()\n", "        except:\n", "            driver.execute_script(\"arguments[0].click();\", tab)\n", "        time.sleep(1.0)\n", "\n", "        try:\n", "            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, PROG_TABLE_ID)))\n", "        except:\n", "            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located(\n", "                (By.XPATH, \"//table[contains(@id,'grd_csProgLst') and contains(@id,'table')]\")\n", "            ))\n", "\n", "        trs = table.find_elements(By.XPATH, \".//tr\")\n", "        for tr in trs:\n", "            tds = tr.find_elements(By.XPATH, \".//td\")\n", "            if len(tds) < 3:\n", "                continue\n", "            date_txt = tds[0].text.strip()\n", "            cont_txt = tds[1].text.strip()\n", "            res_txt  = tds[2].text.strip()\n", "            if not date_txt:\n", "                continue\n", "            out_rows.append({\n", "                \"사건번호\": case_no,\n", "                \"일자\": date_txt,\n", "                \"내용\": cont_txt,\n", "                \"결과\": res_txt\n", "            })\n", "    except Exception as e:\n", "        print(f\"[WARN] 진행내용 파싱 중 예외: {e}\")\n", "    return out_rows\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 메인 크롤링\n", "# ─────────────────────────────────────────────────────\n", "def get_case_count() -> int:\n", "    rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, CASE_ROWS_XPATH)))\n", "    return len(rows)\n", "\n", "def crawl_all_cases_by_index():\n", "    all_general_rows  = []\n", "    all_progress_rows = []\n", "\n", "    total = get_case_count()\n", "    print(f\"[INFO] 사건 건수: {total}건\")\n", "\n", "    for i in range(total):\n", "        try:\n", "            print(f\"\\n[DEBUG] {i+1}/{total}번째 사건 상세 진입 시도\")\n", "            case_no_from_list = click_case_by_index(i)\n", "\n", "            full_text = extract_general_text()\n", "            if not full_text:\n", "                print(\"[WARN] 일반내용 텍스트 추출 실패 → 본 사건 스킵\")\n", "                if len(driver.window_handles) > 1:\n", "                    driver.close(); driver.switch_to.window(driver.window_handles[0])\n", "                time.sleep(0.5)\n", "                continue\n", "\n", "            case_no = pick_case_no_from_text(case_no_from_list) or pick_case_no_from_text(full_text) or case_no_from_list\n", "            case_no = normalize_case_no(case_no)\n", "\n", "            # 일반내용 행 (타임스탬프는 업로드에서 자동 주입)\n", "            row_general = {\n", "                \"사건번호\": case_no,\n", "                \"전체문자열\": full_text,\n", "            }\n", "            all_general_rows.append(row_general)\n", "\n", "            # 진행내용 행들 (타임스탬프는 업로드에서 자동 주입)\n", "            prog_rows = click_progress_tab_and_extract_table(case_no)\n", "            all_progress_rows.extend(prog_rows)\n", "\n", "        except Exception as e:\n", "            print(f\"[WARN] 사건({i}) 처리 중 예외: {e}\")\n", "        finally:\n", "            if len(driver.window_handles) > 1:\n", "                driver.close(); driver.switch_to.window(driver.window_handles[0])\n", "            time.sleep(0.5)\n", "\n", "    # ── Data<PERSON><PERSON>e 및 컬럼 순서 고정 ─────────────────\n", "    df_general_out  = pd.DataFrame(all_general_rows)\n", "    df_progress_out = pd.DataFrame(all_progress_rows)\n", "\n", "    if not df_general_out.empty:\n", "        desired_cols_gen = [\"사건번호\", \"전체문자열\"]  # '크롤링일시'는 템플릿 마지막 열 자동 주입\n", "        df_general_out = df_general_out.reindex(columns=desired_cols_gen)\n", "\n", "    if not df_progress_out.empty and \"일자\" in df_progress_out.columns:\n", "        try:\n", "            s = pd.to_datetime(df_progress_out[\"일자\"], errors=\"coerce\")\n", "            df_progress_out.loc[s.notna(), \"일자\"] = s[s.notna()].dt.strftime(\"%Y-%m-%d\")\n", "            df_progress_out = df_progress_out.sort_values([\"사건번호\", \"일자\"], kind=\"stable\")\n", "        except Exception:\n", "            pass\n", "\n", "    # ── 구글 스프레드시트 업로드 ──\n", "    if df_general_out.empty and df_progress_out.empty:\n", "        print(\"\\n[⚠️] 업로드할 데이터가 없습니다.\")\n", "    else:\n", "        upload_logs_to_sheets(df_prog=df_progress_out, df_gen=df_general_out)\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 실행\n", "# ─────────────────────────────────────────────────────\n", "if __name__ == \"__main__\":\n", "    try:\n", "        login()\n", "        go_to_my_ecfs()\n", "        go_to_interest_case()\n", "        crawl_all_cases_by_index()\n", "    finally:\n", "        print(\"[DONE]\")\n", "        try:\n", "            driver.quit()\n", "        except:\n", "            pass\n"]}], "metadata": {"kernelspec": {"display_name": "layer-crawling", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}