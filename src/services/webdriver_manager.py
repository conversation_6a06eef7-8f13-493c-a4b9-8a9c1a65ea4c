"""WebDriver management service."""

import time
from typing import Optional
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import WebDriverException, TimeoutException

from ..models.config import WebDriverConfig
from ..utils.logger import get_logger
from ..utils.exceptions import WebDriverError


class WebDriverManager:
    """Service for managing Selenium WebDriver instances."""
    
    def __init__(self, config: WebDriverConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self._driver: Optional[webdriver.Chrome] = None
        self._wait: Optional[WebDriverWait] = None
        self._action_chains: Optional[ActionChains] = None
    
    def create_driver(self) -> webdriver.Chrome:
        """
        Create and configure a Chrome WebDriver instance.
        
        Returns:
            webdriver.Chrome: Configured WebDriver instance
            
        Raises:
            WebDriverError: If driver creation fails
        """
        try:
            # Configure Chrome options
            chrome_options = Options()
            
            if self.config.headless:
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-gpu")
            
            # Window size
            chrome_options.add_argument(f"--window-size={self.config.window_width},{self.config.window_height}")
            
            # User agent
            chrome_options.add_argument(f"--user-agent={self.config.user_agent}")
            
            # Additional options for stability
             # 문제 해결을 위한 추가 옵션들
            import tempfile
            import uuid
            
            # 고유한 사용자 데이터 디렉토리 생성
            temp_dir = tempfile.gettempdir()
            user_data_dir = f"{temp_dir}/chrome_user_data_{uuid.uuid4().hex[:8]}"
            chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
            
            # 추가 안정성 옵션들
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")  # 성능 향상
            chrome_options.add_argument("--disable-javascript")  # 필요시
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--no-default-browser-check")
            chrome_options.add_argument("--disable-default-apps")
            
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            # detach 옵션 제거 또는 조건부 적용
            if not self.config.headless:  # headless가 아닐 때만 detach 사용
                chrome_options.add_experimental_option("detach", True)
            
            # Create driver
            self._driver = webdriver.Chrome(options=chrome_options)
            
            # Configure timeouts
            self._driver.implicitly_wait(self.config.implicit_wait_seconds)
            self._driver.set_page_load_timeout(self.config.page_load_timeout_seconds)
            
            # Initialize WebDriverWait and ActionChains
            self._wait = WebDriverWait(self._driver, self.config.timeout_seconds)
            self._action_chains = ActionChains(self._driver)
            
            # Execute script to hide automation indicators
            self._driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info(
                "WebDriver created successfully",
                extra={
                    "headless": self.config.headless,
                    "window_size": f"{self.config.window_width}x{self.config.window_height}"
                }
            )
            
            return self._driver
            
        except WebDriverException as e:
            self.logger.error(f"Failed to create WebDriver: {e}")
            raise WebDriverError(f"Failed to create WebDriver: {e}") from e
        except Exception as e:
            self.logger.error(f"Unexpected error creating WebDriver: {e}")
            raise WebDriverError(f"Unexpected error creating WebDriver: {e}") from e
    
    @property
    def driver(self) -> webdriver.Chrome:
        """Get WebDriver instance (lazy initialization)."""
        if self._driver is None:
            self.create_driver()
        return self._driver
    
    @property
    def wait(self) -> WebDriverWait:
        """Get WebDriverWait instance."""
        if self._wait is None:
            if self._driver is None:
                self.create_driver()
            self._wait = WebDriverWait(self._driver, self.config.timeout_seconds)
        return self._wait
    
    @property
    def action_chains(self) -> ActionChains:
        """Get ActionChains instance."""
        if self._action_chains is None:
            if self._driver is None:
                self.create_driver()
            self._action_chains = ActionChains(self._driver)
        return self._action_chains
    
    def navigate_to(self, url: str) -> None:
        """
        Navigate to a URL with error handling.
        
        Args:
            url: URL to navigate to
            
        Raises:
            WebDriverError: If navigation fails
        """
        try:
            self.logger.info(f"Navigating to: {url}")
            self.driver.get(url)
            self.logger.debug("Navigation completed")
            
        except TimeoutException:
            self.logger.error(f"Timeout navigating to: {url}")
            raise WebDriverError(f"Timeout navigating to: {url}")
        except WebDriverException as e:
            self.logger.error(f"WebDriver error navigating to {url}: {e}")
            raise WebDriverError(f"WebDriver error navigating to {url}: {e}") from e
    
    def wait_for_element(self, by: By, value: str, timeout: Optional[int] = None):
        """
        Wait for an element to be present and return it.
        
        Args:
            by: Selenium By locator type
            value: Locator value
            timeout: Custom timeout (uses default if None)
            
        Returns:
            WebElement: Found element
            
        Raises:
            WebDriverError: If element not found within timeout
        """
        try:
            wait_instance = self.wait
            if timeout:
                wait_instance = WebDriverWait(self.driver, timeout)
            
            element = wait_instance.until(EC.presence_of_element_located((by, value)))
            self.logger.debug(f"Element found: {by}={value}")
            return element
            
        except TimeoutException:
            self.logger.error(f"Element not found within timeout: {by}={value}")
            raise WebDriverError(f"Element not found within timeout: {by}={value}")
        except WebDriverException as e:
            self.logger.error(f"WebDriver error waiting for element {by}={value}: {e}")
            raise WebDriverError(f"WebDriver error waiting for element: {e}") from e
    
    def wait_for_clickable(self, by: By, value: str, timeout: Optional[int] = None):
        """
        Wait for an element to be clickable and return it.
        
        Args:
            by: Selenium By locator type
            value: Locator value
            timeout: Custom timeout (uses default if None)
            
        Returns:
            WebElement: Clickable element
            
        Raises:
            WebDriverError: If element not clickable within timeout
        """
        try:
            wait_instance = self.wait
            if timeout:
                wait_instance = WebDriverWait(self.driver, timeout)
            
            element = wait_instance.until(EC.element_to_be_clickable((by, value)))
            self.logger.debug(f"Clickable element found: {by}={value}")
            return element
            
        except TimeoutException:
            self.logger.error(f"Element not clickable within timeout: {by}={value}")
            raise WebDriverError(f"Element not clickable within timeout: {by}={value}")
        except WebDriverException as e:
            self.logger.error(f"WebDriver error waiting for clickable element {by}={value}: {e}")
            raise WebDriverError(f"WebDriver error waiting for clickable element: {e}") from e
    
    def safe_click(self, element, use_javascript: bool = False) -> None:
        """
        Safely click an element with retry logic.
        
        Args:
            element: WebElement to click
            use_javascript: Whether to use JavaScript click
            
        Raises:
            WebDriverError: If click fails after retries
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if use_javascript:
                    self.driver.execute_script("arguments[0].click();", element)
                else:
                    element.click()
                self.logger.debug("Element clicked successfully")
                return
                
            except WebDriverException as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Click attempt {attempt + 1} failed, retrying: {e}")
                    time.sleep(0.5)
                else:
                    self.logger.error(f"Failed to click element after {max_retries} attempts: {e}")
                    raise WebDriverError(f"Failed to click element: {e}") from e
    
    def take_screenshot(self, filename: str) -> None:
        """
        Take a screenshot for debugging purposes.
        
        Args:
            filename: Filename to save screenshot
        """
        try:
            self.driver.save_screenshot(filename)
            self.logger.info(f"Screenshot saved: {filename}")
        except Exception as e:
            self.logger.warning(f"Failed to take screenshot: {e}")
    
    def cleanup(self) -> None:
        """Clean up WebDriver resources."""
        if self._driver:
            try:
                self._driver.quit()
                self.logger.info("WebDriver cleaned up successfully")
            except Exception as e:
                self.logger.warning(f"Error during WebDriver cleanup: {e}")
            finally:
                self._driver = None
                self._wait = None
                self._action_chains = None
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
