"""Decorators for handling common WebDriver scenarios."""

import functools
import time
from typing import Callable, Any

from .logger import get_logger
from .exceptions import WebDriverError


def handle_cert_installation_popup(popup_handler_method: str = '_handle_cert_installation_popup'):
    """
    Decorator that handles certificate installation popups when WebDriverError occurs and retries once.
    
    This decorator is specifically designed to handle popups that appear when certificate programs
    are not installed, typically showing "아니요" (No) buttons that need to be clicked to continue.
    
    Args:
        popup_handler_method: Name of the method to call for popup handling
                             (default: '_handle_cert_installation_popup')
    
    Usage:
        @handle_cert_installation_popup()
        def some_webdriver_method(self):
            # Method that might trigger cert installation popup
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs) -> Any:
            logger = get_logger(f"{self.__class__.__name__}.{func.__name__}")
            
            try:
                return func(self, *args, **kwargs)
            except WebDriverError as e:
                logger.warning(
                    f"WebDriverError occurred in {func.__name__}: {e}, checking for certificate installation popup"
                )
                
                # Get the popup handler method
                popup_handler = getattr(self, popup_handler_method, None)
                if popup_handler is None:
                    logger.error(f"Popup handler method '{popup_handler_method}' not found in {self.__class__.__name__}")
                    raise e
                
                # Try to handle popup
                try:
                    popup_handled = popup_handler()
                    if popup_handled:
                        logger.info(f"Certificate installation popup handled in {func.__name__}, retrying operation once")
                        time.sleep(0.5)  # Brief pause after popup handling
                        return func(self, *args, **kwargs)  # Retry once
                    else:
                        logger.debug(f"No certificate installation popup found in {func.__name__}, re-raising original error")
                        raise e
                except Exception as popup_error:
                    logger.error(f"Error during popup handling in {func.__name__}: {popup_error}")
                    raise e  # Re-raise original error
                    
        return wrapper
    return decorator


def retry_on_webdriver_error(max_retries: int = 1, delay: float = 0.5):
    """
    Generic decorator for retrying operations on WebDriverError.
    
    Args:
        max_retries: Maximum number of retries (default: 1)
        delay: Delay between retries in seconds (default: 0.5)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs) -> Any:
            logger = get_logger(f"{self.__class__.__name__}.{func.__name__}")
            
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(self, *args, **kwargs)
                except WebDriverError as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(
                            f"WebDriverError in {func.__name__} (attempt {attempt + 1}/{max_retries + 1}): {e}, retrying in {delay}s"
                        )
                        time.sleep(delay)
                    else:
                        logger.error(f"WebDriverError in {func.__name__} failed after {max_retries + 1} attempts: {e}")
            
            raise last_exception
                    
        return wrapper
    return decorator
