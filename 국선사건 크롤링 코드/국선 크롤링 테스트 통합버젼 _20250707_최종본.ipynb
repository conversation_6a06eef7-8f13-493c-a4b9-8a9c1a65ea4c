from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains  #키보드 버튼 누르는 것 처럼 인식 시킬수 있는 옵션
import time
import random
import pyautogui
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta #timedelta 특정날짜 조회를 위한 옵션
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from gspread_formatting import format_cell_range, CellFormat, Color

options = Options()
options.add_argument("--start-maximized") # 시작시 최대창 출력
#options.add_argument("user-data-dir=C:\\user_data\\kimhs") #로그인 설정을 유지해 주는 옵션
options.add_experimental_option("detach", True) #셀레니움 자동종료 막는 옵션

driver = webdriver.Chrome(options=options)
action = ActionChains(driver)

sleep_time = random.uniform(1, 2.5)
url = "https://guksun.scourt.go.kr/pkj/index.on" #국선 사이트 접속

driver.get(url)
query = driver.find_element(By.ID, "mf_ibx_userId") #아이디 입력
query.send_keys("lawyerkk94")

time.sleep(sleep_time)
query = driver.find_element(By.ID, "mf_ibx_userPw") #비밀번호 입력
query.send_keys("kkcho0904!")
time.sleep(sleep_time)
query = driver.find_element(By.ID, "mf_btn_login") #로그인 버튼 클릭
query.click()
time.sleep(2.0)



#현재 상태 : 새로운 창이 떴을 때 요소 값을 찾을 수 없어서 강제로 마우스 클릭형태로 변형해서 사용 중
# To-Do 화면해상도 관계없이 동일하게 적용할 수 있도록 개선 필요
pyautogui.moveTo(1092, 741, 0.5) #중복 로그인 시 확인 버튼 클릭 
pyautogui.click()
time.sleep(sleep_time)

query = driver.find_element(By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_rad_dayDvs_input_1") #선정일 버튼 클릭
query.click()

#선고일 버튼 클릭 추가 







# 오늘, 7일전 날짜 계산 
today = datetime.today()
seven_days_ago = today - timedelta(days=7)

# 화면에 맞춰 'YYYY.MM.DD' 형식으로 변환
fmt = "%Y.%m.%d"
today_str = today.strftime(fmt)          
past7_str = seven_days_ago.strftime(fmt) 

time.sleep(2)

# 날짜 입력창 요소 찾기
start_input = driver.find_element(By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_cal_bgngYmd_input")  # 시작일 (과거 7일 전)
end_input   = driver.find_element(By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_cal_endYmd_input")   # 종료일 (오늘)

ac = ActionChains(driver)

def replace_with(element, text):
    element.clear()                      # 기존 내용 삭제
    ac.move_to_element(element)          \
      .click()                           \
      .send_keys(text)                   \
      .pause(0.1)                        # UI 반응 여유

replace_with(start_input, past7_str)  # ① 시작일 = 7일 전
replace_with(end_input,   today_str)  # ② 종료일 = 오늘

ac.perform()

time.sleep(sleep_time)
query = driver.find_element(By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_btn_search") #조회 버튼 클릭
query.click()

time.sleep(0.5)



# ─────────────────────────────────────────
# 2) 목록 전체 로드 (무한 스크롤) 후 행 수 계산
# ─────────────────────────────────────────
wrapper_id   = "mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_body_table"   # 테이블 ID
row_selector = f"#{wrapper_id} tr.grid_body_row"              # 실제 데이터 행)
scroll_div   = driver.find_element(By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_dataLayer")  # 스크롤 바 div

# 2‑1) 테이블 자체 로드 대기
WebDriverWait(driver, 15).until(
    EC.presence_of_element_located((By.ID, wrapper_id))
)

# 2‑2) 스크롤을 맨 끝까지 내려가며 전체 행을 모두 로드
last_count = -1
while True:
    rows_now = driver.find_elements(By.CSS_SELECTOR, row_selector)
    cur_count = len(rows_now)
    if cur_count == last_count:        # 더 이상 증가 없음 → 스크롤 완료
        break
    last_count = cur_count
    driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", scroll_div)
    time.sleep(0.6)  # 데이터 추가 로드 대기

row_count = last_count
print(f"📋 스크롤 완료 후 전체 행 수: {row_count}")



# ─────────────────────────────────────────
# 3) 사건별 상세 정보 수집
# ─────────────────────────────────────────
labels = [
    "재판부, 재판장", "선정일", "사건번호", "사건명", "피고인명",
    "성별, 나이", "구속여부", "보석여부", "기소일", "선고일", "선고결과"
]
all_results = []

for i in range(row_count):
    try:
        link_id = f"mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_link_{i}_3"
        query = WebDriverWait(driver, 15).until(EC.element_to_be_clickable((By.ID, link_id)))
        driver.execute_script("arguments[0].click();", query)
        print(f"[{i+1}] 사건 클릭 완료")

        # 상세페이지 로딩 대기
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.XPATH, "//th[span[text()='재판부, 재판장']]"))
        )
        time.sleep(1)  # 상세 페이지 렌더링 여유

        result = {}
        for label in labels:
            try:
                th = driver.find_element(By.XPATH, f"//th[span[text()='{label}']]")
                td = th.find_element(By.XPATH, "following-sibling::td[1]")
                result[label] = td.text.strip()
            except:
                result[label] = "Not Found"

        all_results.append(result)
        print(f"[{i+1}] 사건 정보 저장 완료")

        # 목록으로 복귀
        driver.find_element(By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_btn_retList").click()
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.ID, wrapper_id))
        )
        time.sleep(0.5)

    except Exception as e:
        print(f"[{i+1}] 처리 실패: {e}")
        continue
 


# 크롤링 결과 저장
now = datetime.now().strftime("%Y.%m.%d_%H:%M")
# 1) DataFrame 만들기
df = pd.DataFrame(all_results)
# 2) CSV로 저장
csv_filename = f"법원_데이터_결과_{now}.csv"
df.to_csv(csv_filename, index=False, encoding="utf-8-sig")  # Excel에서 한글 깨짐 방지용 BOM
print(f"결과 저장 완료: {csv_filename}")

driver.quit()



# ------------------ 구글 시트 연결 ------------------
scope = [
    "https://spreadsheets.google.com/feeds",
    "https://www.googleapis.com/auth/drive",
]
creds  = ServiceAccountCredentials.from_json_keyfile_name("credentials.json", scope)
client = gspread.authorize(creds)

sheet_name = "국선 법원 크롤링"
try:
    ss = client.open(sheet_name)
except gspread.SpreadsheetNotFound:
    ss = client.create(sheet_name)
    ss.share(None, perm_type="anyone", role="writer")

# ------------------ 워크시트 선택/생성 ------------------
try:
    ws = ss.worksheet("국선선정내역크롤링로그")
except gspread.exceptions.WorksheetNotFound:
    ws = ss.add_worksheet(title="국선선정내역크롤링로그", rows="100", cols="20")

# ------------------ 1. DataFrame 정리 ------------------
TIMESTAMP_COL = '업데이트 시점'   # ← 이미 들어 와 있을 수 있는 열 이름
if TIMESTAMP_COL in df.columns:
    df = df.drop(columns=[TIMESTAMP_COL])   # ★ 중복 타임스탬프 제거

# ------------------ 2. 타임스탬프를 쓸 열 위치 ------------------
TARGET_COL_INDEX = 12         # L열 (A=1 … L=11)

# ------------------ 3. 헤더 생성 (없을 때만) ------------------
if not ws.get_all_values():  # 시트가 비어 있는 경우
    padding = [''] * (TARGET_COL_INDEX - len(df.columns) - 1)
    header  = df.columns.tolist() + padding + [TIMESTAMP_COL]
    ws.append_row(header)

# ------------------ 4. 데이터 + 타임스탬프 준비 ------------------
timestamp = datetime.now().strftime('%Y.%m.%d %H:%M')

rows_with_ts = [
    row + [''] * (TARGET_COL_INDEX - len(row) - 1) + [timestamp]
    for row in df.values.tolist()
]

# ------------------ 5. 시트 하단에 A열부터 일괄 추가 ------------------
ws.append_rows(
    rows_with_ts,
    value_input_option="RAW",
    insert_data_option="INSERT_ROWS",  # 하단에 삽입
    table_range="A1"                   # ★ A열 고정
)

print("구글 시트 반영 완료 (타임스탬프 → L열)")