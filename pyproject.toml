[project]
name = "layer-crawling"
version = "0.1.0"
description = "Scalable court case crawling system for Google Cloud Run"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # Web scraping and automation
    "selenium>=4.15.2",
    "beautifulsoup4>=4.12.2",
    "requests>=2.31.0",
    # Google Cloud services
    "google-cloud-secret-manager>=2.18.1",
    "google-cloud-logging>=3.8.0",
    "google-cloud-error-reporting>=1.9.3",
    # Google Sheets integration
    "gspread>=5.12.0",
    "oauth2client>=4.1.3",
    "gspread-formatting>=1.1.2",
    # Data processing
    "pandas>=2.1.3",
    "pyperclip>=1.8.2",
    # Web framework for HTTP triggers
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    # Configuration and environment
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    # Utilities
    "pytz>=2023.3",
    "python-dateutil>=2.8.2",
    # Logging
    "structlog>=23.2.0",
    "ruff>=0.12.10",
    "pyautogui>=0.9.54",
    "webdriver-manager>=4.0.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "black>=23.11.0",
    "ruff>=0.1.6",
    "mypy>=1.7.1",
    "httpx>=0.25.2",  # for testing FastAPI
]

[project.scripts]
crawl-public-defender = "src.main:crawl_public_defender_cli"
crawl-electronic-litigation = "src.main:crawl_electronic_litigation_cli"
start-server = "src.main:start_server_cli"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.ruff]
target-version = "py312"
line-length = 88
select = ["E", "F", "I", "N", "W", "UP"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=src --cov-report=term-missing"
