#!/bin/bash

# Deployment script for Court Crawling System to Google Cloud Run
# This script builds and deploys the application

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"crawling-law"}
REGION=${REGION:-"asia-northeast3"}
SERVICE_NAME="court-crawling-system"
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Deploying Court Crawling System to Google Cloud Run${NC}"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Service Name: $SERVICE_NAME"
echo "Image: $IMAGE_NAME"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}Error: gcloud CLI is not installed${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}Error: Docker is not running${NC}"
    exit 1
fi

# Set the project
echo -e "${YELLOW}Setting project...${NC}"
gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${YELLOW}Enabling required APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Grant Cloud Build service account necessary permissions
echo -e "${YELLOW}Setting up Cloud Build permissions...${NC}"
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$<EMAIL>" \
    --role="roles/storage.admin" \
    --quiet || echo "Permission already exists"
    
# Build and push the Docker image using Cloud Build
echo -e "${YELLOW}Building Docker image with Cloud Build...${NC}"
gcloud builds submit --tag $IMAGE_NAME --timeout=20m

# Check if the image was built successfully
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to build Docker image${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker image built successfully${NC}"

# Deploy to Cloud Run
echo -e "${YELLOW}Deploying to Cloud Run...${NC}"
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME \
    --platform managed \
    --region $REGION \
    --service-account "court-crawling-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --memory 2Gi \
    --cpu 2 \
    --timeout 900 \
    --concurrency 1 \
    --max-instances 10 \
    --min-instances 0 \
    --port 8080 \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID,ENVIRONMENT=production,WEBDRIVER_HEADLESS=true,LOG_LEVEL=INFO,LOG_FORMAT=json" \
    --allow-unauthenticated \
    --quiet

# Check if deployment was successful
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to deploy to Cloud Run${NC}"
    exit 1
fi

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo ""
echo -e "${GREEN}Service URL: $SERVICE_URL${NC}"
echo ""
echo "Available endpoints:"
echo "  GET  $SERVICE_URL/health"
echo "  POST $SERVICE_URL/crawl/public-defender"
echo "  POST $SERVICE_URL/crawl/electronic-litigation"
echo "  POST $SERVICE_URL/crawl/all"
echo ""
echo "Test the health endpoint:"
echo "curl $SERVICE_URL/health"
echo ""
echo "Test crawling (replace YOUR_API_KEY if authentication is enabled):"
echo "curl -X POST $SERVICE_URL/crawl/public-defender \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_API_KEY' \\"
echo "  -d '{\"date_range_days\": 7}'"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Set up Cloud Scheduler jobs: ./deploy/setup-scheduler.sh"
echo "2. Configure monitoring and alerting"
echo "3. Set up API authentication if needed"
echo "4. Test the endpoints"

# Save deployment info
cat > deployment-info.txt << EOF
Deployment Information
=====================
Date: $(date)
Project ID: $PROJECT_ID
Region: $REGION
Service Name: $SERVICE_NAME
Image: $IMAGE_NAME
Service URL: $SERVICE_URL

Endpoints:
- Health Check: GET $SERVICE_URL/health
- Public Defender Crawl: POST $SERVICE_URL/crawl/public-defender
- Electronic Litigation Crawl: POST $SERVICE_URL/crawl/electronic-litigation
- Combined Crawl: POST $SERVICE_URL/crawl/all
EOF

echo -e "${GREEN}Deployment information saved to deployment-info.txt${NC}"
