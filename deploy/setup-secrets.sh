#!/bin/bash

# Setup script for Google Cloud Secret Manager secrets
# Run this script to create the required secrets for the court crawling system

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"crawling-law"}
REGION=${REGION:-"asia-northeast3"}

echo "Setting up secrets for project: $PROJECT_ID"

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "Error: gcloud CLI is not installed"
    exit 1
fi

# Check for required environment variables for credentials
if [ -z "$LAWYER_USERNAME" ] || [ -z "$LAWYER_PASSWORD" ] || [ -z "$ELITIGATION_USERNAME" ] || [ -z "$ELITIGATION_PASSWORD" ]; then
    echo "Error: Required credential environment variables are not set."
    echo "Please set: LAWYER_USERNAME, LAWYER_PASSWORD, ELITIGATION_USERNAME, ELITIGATION_PASSWORD"
    echo "You can create a .env file and run 'source .env' before executing this script."
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "Enabling required APIs..."
gcloud services enable secretmanager.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable monitoring.googleapis.com

# Create lawyer accounts secret
echo "Creating lawyer accounts secret..."
cat > /tmp/lawyer-accounts.json << EOF
{
  "accounts": [
    {
      "username": "$LAWYER_USERNAME",
      "password": "$LAWYER_PASSWORD",
      "assigned_courts": [],
      "active": true
    }
  ]
}
EOF

if gcloud secrets describe lawyer-accounts &>/dev/null; then
    echo "Secret 'lawyer-accounts' already exists. Adding a new version..."
    gcloud secrets versions add lawyer-accounts --data-file=/tmp/lawyer-accounts.json
else
    echo "Creating secret 'lawyer-accounts'..."
    gcloud secrets create lawyer-accounts --data-file=/tmp/lawyer-accounts.json --replication-policy=automatic
fi
rm /tmp/lawyer-accounts.json

# Create electronic litigation credentials secret
echo "Creating electronic litigation credentials secret..."
cat > /tmp/electronic-litigation-credentials.json << EOF
{
  "username": "$ELITIGATION_USERNAME",
  "password": "$ELITIGATION_PASSWORD"
}
EOF

if gcloud secrets describe electronic-litigation-credentials &>/dev/null; then
    echo "Secret 'electronic-litigation-credentials' already exists. Adding a new version..."
    gcloud secrets versions add electronic-litigation-credentials --data-file=/tmp/electronic-litigation-credentials.json
else
    echo "Creating secret 'electronic-litigation-credentials'..."
    gcloud secrets create electronic-litigation-credentials --data-file=/tmp/electronic-litigation-credentials.json --replication-policy=automatic
fi
rm /tmp/electronic-litigation-credentials.json

# Create Google Sheets credentials secret (you need to provide your own service account key)
echo "Creating Google Sheets credentials secret..."
echo "Please ensure you have a service account key file named 'google-sheets-credentials.json'"
echo "If you don't have one, create a service account in the Google Cloud Console and download the key"

if [ -f "google-sheets-credentials.json" ]; then
    if gcloud secrets describe google-sheets-credentials &>/dev/null; then
        echo "Secret 'google-sheets-credentials' already exists. Adding a new version..."
        gcloud secrets versions add google-sheets-credentials --data-file=google-sheets-credentials.json
    else
        echo "Creating secret 'google-sheets-credentials'..."
        gcloud secrets create google-sheets-credentials --data-file=google-sheets-credentials.json --replication-policy=automatic
    fi
else
    echo "Warning: google-sheets-credentials.json not found. You'll need to create this secret manually."
fi

# Create service account for Cloud Run
echo "Creating service account..."
gcloud iam service-accounts create court-crawling-sa \
    --display-name="Court Crawling System Service Account" \
    --description="Service account for the court crawling system" || echo "Service account already exists"

# Grant necessary permissions
echo "Granting permissions to service account..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:court-crawling-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:court-crawling-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:court-crawling-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/monitoring.metricWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:court-crawling-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/errorreporting.writer"

echo "Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Build and deploy the Docker image:"
echo "   gcloud builds submit --tag gcr.io/$PROJECT_ID/court-crawling-system"
echo ""
echo "2. Deploy to Cloud Run:"
echo "   gcloud run deploy court-crawling-system \\"
echo "     --image gcr.io/$PROJECT_ID/court-crawling-system \\"
echo "     --platform managed \\"
echo "     --region $REGION \\"
echo "     --service-account court-crawling-sa@$PROJECT_ID.iam.gserviceaccount.com \\"
echo "     --memory 2Gi \\"
echo "     --cpu 2 \\"
echo "     --timeout 900 \\"
echo "     --concurrency 1 \\"
echo "     --max-instances 10 \\"
echo "     --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,ENVIRONMENT=production,WEBDRIVER_HEADLESS=true"
