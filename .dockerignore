# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
docs/

# Test files
tests/
.pytest_cache/
.coverage
htmlcov/

# Logs
*.log
logs/

# Screenshots
screenshots/

# Jupyter notebooks
*.ipynb
국선사건 크롤링 코드/
전자소송 크롤링 코드/

# Temporary files
*.tmp
*.temp

# Docker
Dockerfile
.dockerignore
docker-compose.yml
