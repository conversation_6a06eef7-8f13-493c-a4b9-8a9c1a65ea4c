프로젝트 완료 요약

✅ 완료된 작업들:
Phase 1: 코드 리팩토링

✅ SOLID 원칙을 따르는 모듈형 아키텍처 구현
✅ 추상 기본 클래스와 인터페이스 생성
✅ 구성 관리 시스템 구현
✅ WebDriver 관리 서비스 구현
✅ 국선 크롤러를 모듈형 클래스로 리팩토링
✅ 전자소송 크롤러를 모듈형 클래스로 리팩토링
✅ Google Sheets 통합 서비스 구현
✅ 포괄적인 로깅 및 오류 처리 추가
Phase 2: 배포 구성

✅ Docker 컨테이너화 (멀티스테이지 빌드)
✅ HTTP 트리거용 FastAPI 애플리케이션 생성
✅ Cloud Run 서비스 구성
✅ Cloud Scheduler 통합 구현
Phase 3: Secret Management 통합

✅ Google Cloud Secret Manager 설정
✅ 변호사 계정 자격 증명 저장
✅ 법원별 필터링 구현
✅ 전자소송 로그인 자격 증명 저장
✅ 서비스 계정 자격 증명 구성
추가 완료 사항:

✅ 배포 스크립트 및 문서 생성
✅ 모니터링 및 알림 설정
✅ 포괄적인 README 및 배포 가이드
🚀 주요 기능:
확장 가능한 아키텍처: uv 패키지 관리, Python 3.12, FastAPI
다중 트리거 메커니즘: HTTP 엔드포인트, Cloud Scheduler, 수동 실행
보안 자격 증명 관리: Google Cloud Secret Manager 통합
강력한 오류 처리: 구조화된 로깅, 재시도 로직, 우아한 실패 처리
모니터링 및 알림: Cloud Logging, Error Reporting, 사용자 정의 메트릭
Docker 컨테이너화: 프로덕션 준비된 멀티스테이지 빌드
📁 프로젝트 구조:
src/
├── crawlers/          # 크롤러 구현
├── services/          # 핵심 서비스
├── models/            # 데이터 모델
├── utils/             # 유틸리티
├── app.py             # FastAPI 애플리케이션
└── main.py            # CLI 진입점

deploy/                # 배포 스크립트
├── setup-secrets.sh
├── setup-scheduler.sh
├── setup-monitoring.sh
└── deploy.sh

🎯 다음 단계:

```bash
# 1. .env 파일의 변수들을 현재 셸 세션에 로드
set -a
source .env
set +a
# 2. 비밀 정보 설정 스크립트 실행
./deploy/setup-secrets.sh
```


Google Cloud 프로젝트 설정:

```bash
export GOOGLE_CLOUD_PROJECT="crawling-law"
./deploy/setup-secrets.sh
```

애플리케이션 배포:

```bash
./deploy/deploy.sh
```

스케줄링 설정:

```bash
./deploy/setup-scheduler.sh
```

모니터링 구성:

```bash
./deploy/setup-monitoring.sh
```

이제 시스템이 완전히 준비되었으며, 기존 Jupyter 노트북의 모든 기능을 유지하면서 확장 가능하고 유지보수 가능한 클라우드 네이티브 애플리케이션으로 변환되었습니다! 🎊

---

Cloud Run 과의 연동 설명
여기서 한 가지 중요한 점을 짚어드립니다. setup-secrets.sh 스크립트는 Google Secret Manager에 비밀 정보를 '저장'하는 역할을 합니다.

일단 비밀 정보가 Secret Manager에 저장되면, Cloud Run 서비스는 직접 아이디와 비밀번호를 환경 변수로 가질 필요가 없습니다. 대신, deploy.sh에서 설정한 court-crawling-sa 서비스 계정의 권한을 통해 Secret Manager에서 안전하게 값을 읽어옵니다.

이것이 바로 현재 애플리케이션(src/services/secret_manager.py)이 동작하는 방식이며, 클라우드 환경에서 보안을 유지하는 가장 이상적인 방법입니다. 따라서 Cloud Run 서비스의 "변수 및 보안 비밀" 탭에 직접 아이디/비밀번호를 입력하실 필요가 없습니다.

---

로컬에서 기능 테스트를 위한 단계별 명령어 순서를 알려드리겠습니다.

로컬에서 기능 테스트를 위한 단계별 명령어 순서를 알려드리겠습니다:

## 🚀 로컬 테스트 단계별 가이드

### 1단계: 환경 설정
```bash
# 1. 의존성 설치 (이미 완료되어 있음)
uv sync

# 2. 환경 변수 파일 생성
cp .env.example .env

# 3. .env 파일 편집 (필수 설정)
# 최소한 다음 값들을 설정해야 합니다:
```

**.env 파일에서 수정해야 할 주요 설정:**
```bash
# Google Cloud 프로젝트 ID (실제 프로젝트 ID로 변경)
GOOGLE_CLOUD_PROJECT=crawling-law

# 로컬 테스트용 설정
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
WEBDRIVER_HEADLESS=false  # 브라우저 창을 보고 싶다면 false로 설정

# API 인증 (선택사항)
API_KEY=test-api-key-123
```

### 2단계: 구성 테스트
```bash
# 설정 로딩 테스트
uv run python -c "from src.services.config_manager import load_config; config = load_config(); print('✅ Configuration loaded successfully')"
```

### 3단계: FastAPI 서버 실행 및 테스트

#### 3-1. 서버 시작
```bash
# 디버그 모드로 FastAPI 서버 시작
uv run python -m src.main start-server --debug --reload

# 또는 직접 uvicorn 사용
uv run uvicorn src.app:app --host 0.0.0.0 --port 8080 --reload --log-level debug
```

#### 3-2. API 엔드포인트 테스트
새 터미널에서 다음 명령어들을 실행:

```bash
# Health check 테스트
curl http://localhost:8080/health

# 예상 응답:
# {"status":"healthy","timestamp":"2024-01-XX...","version":"0.1.0"}
```

### 4단계: CLI 명령어 테스트

#### 4-1. 도움말 확인
```bash
# 전체 CLI 도움말
uv run python -m src.main --help

# 개별 명령어 도움말
uv run python -m src.main crawl-public-defender --help
uv run python -m src.main crawl-electronic-litigation --help
```

#### 4-2. 크롤러 개별 테스트 (Secret Manager 없이)
**주의:** 실제 크롤링은 Google Cloud Secret Manager가 설정되어야 작동합니다.

```bash
# 국선사건 크롤러 테스트 (설정 로딩까지만)
uv run python -c "
from src.services.config_manager import load_config
from src.services.webdriver_manager import WebDriverManager
config = load_config()
webdriver_manager = WebDriverManager(config.webdriver)
print('✅ WebDriver manager created successfully')
"

# 전자소송 크롤러 테스트 (설정 로딩까지만)
uv run python -c "
from src.crawlers.electronic_litigation import ElectronicLitigationCrawler
from src.services.webdriver_manager import WebDriverManager
from src.services.config_manager import load_config
config = load_config()
webdriver_manager = WebDriverManager(config.webdriver)
crawler = ElectronicLitigationCrawler(config.electronic_litigation, webdriver_manager)
print('✅ Electronic litigation crawler created successfully')
"
```

### 5단계: 브라우저 테스트
```bash
# 브라우저가 정상적으로 실행되는지 테스트
uv run python -c "
from src.services.config_manager import load_config
from src.services.webdriver_manager import WebDriverManager
config = load_config()
config.webdriver.headless = False  # 브라우저 창 표시
webdriver_manager = WebDriverManager(config.webdriver)
with webdriver_manager:
    webdriver_manager.navigate_to('https://www.google.com')
    print('✅ Browser test successful')
    input('Press Enter to close browser...')
"
```

### 6단계: API 엔드포인트 상세 테스트

#### 6-1. 인증 없이 테스트
```bash
# 국선사건 크롤링 API 테스트 (실패 예상 - Secret Manager 미설정)
curl -X POST http://localhost:8080/crawl/public-defender \
  -H "Content-Type: application/json" \
  -d '{"date_range_days": 7}'

# 전자소송 크롤링 API 테스트 (실패 예상 - Secret Manager 미설정)
curl -X POST http://localhost:8080/crawl/electronic-litigation \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### 6-2. API 키 인증 테스트 (API_KEY 설정한 경우)
```bash
curl -X POST http://localhost:8080/crawl/public-defender \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-api-key-123" \
  -d '{"date_range_days": 7}'
```

### 7단계: 로그 확인
```bash
# 서버 실행 중 로그를 다른 터미널에서 확인
# (서버가 실행 중인 터미널에서 로그가 실시간으로 출력됨)

# 또는 파일로 로그 저장하여 실행
uv run python -m src.main start-server --debug 2>&1 | tee server.log
```

### 8단계: 개발 도구 테스트
```bash
# 코드 포맷팅 테스트
uv run black src/ --check

# 린팅 테스트
uv run ruff check src/

# 타입 체킹 테스트 (선택사항)
uv run mypy src/ --ignore-missing-imports
```

## 🔧 문제 해결

### 일반적인 문제들:

1. **Chrome/ChromeDriver 문제:**
   ```bash
   # Chrome 설치 확인 (macOS)
   /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version
   
   # ChromeDriver 자동 설치 (webdriver-manager 사용)
   uv add webdriver-manager
   ```

2. **포트 충돌:**
   ```bash
   # 다른 포트 사용
   uv run python -m src.main start-server --port 8081
   ```

3. **환경 변수 문제:**
   ```bash
   # 환경 변수 확인
   uv run python -c "import os; print('GOOGLE_CLOUD_PROJECT:', os.getenv('GOOGLE_CLOUD_PROJECT'))"
   ```

## 📝 테스트 체크리스트

- [ ] ✅ 의존성 설치 완료
- [ ] ✅ .env 파일 설정 완료
- [ ] ✅ 설정 로딩 테스트 통과
- [ ] ✅ FastAPI 서버 시작 성공
- [ ] ✅ Health check API 응답 확인
- [ ] ✅ WebDriver 생성 테스트 통과
- [ ] ✅ 브라우저 실행 테스트 통과
- [ ] ✅ CLI 명령어 도움말 확인
- [ ] ✅ 로그 출력 정상 확인

이 순서대로 진행하시면 로컬에서 시스템의 기본 기능들을 테스트할 수 있습니다. 실제 크롤링 기능을 완전히 테스트하려면 Google Cloud Secret Manager 설정이 필요합니다.
